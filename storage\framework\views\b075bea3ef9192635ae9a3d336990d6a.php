<?php $__env->startSection('title', $service->title . ' - Service Details'); ?>
<?php $__env->startSection('description', $service->short_description ?: Str::limit($service->description, 160)); ?>

<?php $__env->startSection('content'); ?>
<!-- Service Hero Section -->
<section class="service-hero-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <nav aria-label="breadcrumb" class="mb-4">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('services')); ?>">Services</a></li>
                        <li class="breadcrumb-item active" aria-current="page"><?php echo e($service->title); ?></li>
                    </ol>
                </nav>
                
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <div class="d-flex align-items-center mb-3">
                            <?php if($service->icon): ?>
                                <i class="<?php echo e($service->icon); ?> fa-3x text-primary me-3"></i>
                            <?php endif; ?>
                            <div>
                                <h1 class="display-5 fw-bold mb-0"><?php echo e($service->title); ?></h1>
                                <?php if($service->is_featured): ?>
                                    <span class="badge bg-warning text-dark mt-2">
                                        <i class="fas fa-star me-1"></i>Featured Service
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <?php if($service->short_description): ?>
                            <p class="lead text-muted mb-4"><?php echo e($service->short_description); ?></p>
                        <?php endif; ?>
                    </div>
                    
                    <div class="col-lg-4 text-end">
                        <?php if($service->price_from): ?>
                            <div class="price-display">
                                <span class="text-muted">Starting from</span>
                                <div class="h2 text-primary fw-bold mb-0">$<?php echo e(number_format($service->price_from, 2)); ?></div>
                                <?php if($service->price_unit): ?>
                                    <small class="text-muted"><?php echo e($service->price_unit); ?></small>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Service Image -->
<?php if($service->image): ?>
<section class="service-image-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <img src="<?php echo e(asset('storage/' . $service->image)); ?>" 
                     alt="<?php echo e($service->title); ?>" 
                     class="img-fluid rounded shadow-lg w-100"
                     style="height: 400px; object-fit: cover;">
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Service Details -->
<section class="section-padding">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <h2 class="h3 fw-bold mb-4">Service Overview</h2>
                <div class="service-description">
                    <?php echo nl2br(e($service->description)); ?>

                </div>
                
                <?php if($service->features && count($service->features) > 0): ?>
                    <h3 class="h4 fw-bold mt-5 mb-3">What's Included</h3>
                    <div class="row g-3">
                        <?php $__currentLoopData = $service->features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-md-6">
                                <div class="d-flex align-items-start">
                                    <i class="fas fa-check-circle text-primary me-2 mt-1"></i>
                                    <span><?php echo e($feature); ?></span>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php endif; ?>
                
                <?php if($service->process_steps && count($service->process_steps) > 0): ?>
                    <h3 class="h4 fw-bold mt-5 mb-4">Our Process</h3>
                    <div class="process-steps">
                        <?php $__currentLoopData = $service->process_steps; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $step): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="process-step d-flex mb-4">
                                <div class="step-number">
                                    <span class="badge bg-primary rounded-circle p-3 fs-5"><?php echo e($index + 1); ?></span>
                                </div>
                                <div class="step-content ms-3">
                                    <h5 class="fw-bold"><?php echo e($step['title'] ?? 'Step ' . ($index + 1)); ?></h5>
                                    <p class="text-muted mb-0"><?php echo e($step['description'] ?? $step); ?></p>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="col-lg-4">
                <!-- Service Info Card -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="h5 mb-0">Service Information</h3>
                    </div>
                    <div class="card-body">
                        <?php if($service->price_from): ?>
                            <div class="mb-3">
                                <strong>Starting Price:</strong><br>
                                $<?php echo e(number_format($service->price_from, 2)); ?>

                                <?php if($service->price_unit): ?>
                                    <small class="text-muted"><?php echo e($service->price_unit); ?></small>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if($service->duration): ?>
                            <div class="mb-3">
                                <strong>Typical Duration:</strong><br>
                                <?php echo e($service->duration); ?>

                            </div>
                        <?php endif; ?>
                        
                        <?php if($service->warranty_period): ?>
                            <div class="mb-3">
                                <strong>Warranty:</strong><br>
                                <?php echo e($service->warranty_period); ?>

                            </div>
                        <?php endif; ?>
                        
                        <div class="mb-3">
                            <strong>Availability:</strong><br>
                            <span class="badge bg-success"><?php echo e($service->is_active ? 'Available' : 'Not Available'); ?></span>
                        </div>
                    </div>
                </div>
                
                <!-- Contact CTA -->
                <div class="card mt-4">
                    <div class="card-body text-center">
                        <h4 class="h5 mb-3">Ready to Get Started?</h4>
                        <p class="text-muted mb-3">Contact us for a personalized quote and consultation</p>
                        <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary btn-lg w-100 mb-2">Get Free Quote</a>
                        <a href="tel:+1234567890" class="btn btn-outline-primary w-100">
                            <i class="fas fa-phone me-2"></i>Call Now
                        </a>
                    </div>
                </div>
                
                <!-- FAQ Section -->
                <?php if($service->faqs && count($service->faqs) > 0): ?>
                <div class="card mt-4">
                    <div class="card-header">
                        <h4 class="h5 mb-0">Frequently Asked Questions</h4>
                    </div>
                    <div class="card-body">
                        <div class="accordion" id="serviceFAQ">
                            <?php $__currentLoopData = $service->faqs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $faq): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="faq<?php echo e($index); ?>">
                                        <button class="accordion-button collapsed" type="button" 
                                                data-bs-toggle="collapse" data-bs-target="#collapse<?php echo e($index); ?>" 
                                                aria-expanded="false" aria-controls="collapse<?php echo e($index); ?>">
                                            <?php echo e($faq['question']); ?>

                                        </button>
                                    </h2>
                                    <div id="collapse<?php echo e($index); ?>" class="accordion-collapse collapse" 
                                         aria-labelledby="faq<?php echo e($index); ?>" data-bs-parent="#serviceFAQ">
                                        <div class="accordion-body">
                                            <?php echo e($faq['answer']); ?>

                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Related Services -->
<section class="section-padding bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h2 class="h3 fw-bold mb-4">Other Services</h2>
                
                <div class="row g-4">
                    <?php
                        $relatedServices = \App\Models\Service::active()
                            ->where('id', '!=', $service->id)
                            ->take(3)
                            ->get();
                    ?>
                    
                    <?php $__currentLoopData = $relatedServices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedService): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-lg-4 col-md-6">
                        <div class="card h-100 text-center">
                            <?php if($relatedService->image): ?>
                                <img src="<?php echo e(asset('storage/' . $relatedService->image)); ?>" 
                                     class="card-img-top" 
                                     alt="<?php echo e($relatedService->title); ?>" 
                                     style="height: 200px; object-fit: cover;">
                            <?php endif; ?>
                            <div class="card-body">
                                <?php if($relatedService->icon): ?>
                                    <i class="<?php echo e($relatedService->icon); ?> fa-2x text-primary mb-3"></i>
                                <?php endif; ?>
                                <h5 class="card-title"><?php echo e($relatedService->title); ?></h5>
                                <p class="card-text"><?php echo e($relatedService->short_description ?: Str::limit($relatedService->description, 100)); ?></p>
                                <?php if($relatedService->price_from): ?>
                                    <p class="text-primary fw-bold">Starting from $<?php echo e(number_format($relatedService->price_from, 2)); ?></p>
                                <?php endif; ?>
                            </div>
                            <div class="card-footer bg-transparent">
                                <a href="<?php echo e(route('service', $relatedService->slug)); ?>" class="btn btn-outline-primary">Learn More</a>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                
                <div class="text-center mt-4">
                    <a href="<?php echo e(route('services')); ?>" class="btn btn-primary">View All Services</a>
                </div>
            </div>
        </div>
    </div>
</section>

<?php $__env->startPush('styles'); ?>
<style>
.service-hero-section {
    padding: 2rem 0;
}

.service-image-section {
    padding: 2rem 0;
}

.service-description {
    font-size: 1.1rem;
    line-height: 1.8;
}

.process-step {
    position: relative;
}

.process-step:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 1.5rem;
    top: 4rem;
    width: 2px;
    height: 2rem;
    background-color: #dee2e6;
}

.step-number .badge {
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.price-display {
    text-align: center;
    padding: 1rem;
    background: rgba(var(--bs-primary-rgb), 0.1);
    border-radius: 0.5rem;
}

.section-padding {
    padding: 4rem 0;
}

@media (max-width: 768px) {
    .section-padding {
        padding: 2rem 0;
    }
    
    .service-hero-section,
    .service-image-section {
        padding: 1rem 0;
    }
}
</style>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\new-project-app\resources\views/service.blade.php ENDPATH**/ ?>