<!-- Admin Header -->
<header class="admin-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <!-- Mobile Menu Toggle -->
            <div class="col-auto d-lg-none">
                <button class="btn btn-link text-dark" id="mobileMenuToggle">
                    <i class="fas fa-bars fa-lg"></i>
                </button>
            </div>

            <!-- Page Title -->
            <div class="col">
                <div class="page-title-section">
                    <h1 class="page-title mb-0">@yield('page-title', 'Dashboard')</h1>
                    @hasSection('page-subtitle')
                        <p class="page-subtitle text-muted mb-0">@yield('page-subtitle')</p>
                    @endif
                </div>
            </div>

            <!-- Header Actions -->
            <div class="col-auto">
                <div class="header-actions d-flex align-items-center">
                    <!-- Notifications -->
                    <div class="dropdown me-3">
                        <button class="btn btn-link text-dark position-relative" 
                                type="button" 
                                id="notificationsDropdown" 
                                data-bs-toggle="dropdown" 
                                aria-expanded="false">
                            <i class="fas fa-bell fa-lg"></i>
                            @php
                                $unreadMessages = \App\Models\Message::where('is_read', false)->count();
                                $totalNotifications = $unreadMessages;
                            @endphp
                            @if($totalNotifications > 0)
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                    {{ $totalNotifications > 99 ? '99+' : $totalNotifications }}
                                </span>
                            @endif
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end notifications-dropdown" aria-labelledby="notificationsDropdown">
                            <li class="dropdown-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>Notifications</span>
                                    @if($totalNotifications > 0)
                                        <span class="badge bg-primary">{{ $totalNotifications }}</span>
                                    @endif
                                </div>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            
                            @if($unreadMessages > 0)
                                <li>
                                    <a class="dropdown-item" href="{{ route('admin.messages') }}">
                                        <div class="d-flex align-items-center">
                                            <div class="notification-icon bg-primary">
                                                <i class="fas fa-envelope"></i>
                                            </div>
                                            <div class="notification-content">
                                                <div class="notification-title">New Messages</div>
                                                <div class="notification-text">{{ $unreadMessages }} unread message{{ $unreadMessages > 1 ? 's' : '' }}</div>
                                            </div>
                                        </div>
                                    </a>
                                </li>
                            @endif
                            
                            @if($totalNotifications === 0)
                                <li>
                                    <div class="dropdown-item-text text-center text-muted py-3">
                                        <i class="fas fa-check-circle fa-2x mb-2"></i><br>
                                        No new notifications
                                    </div>
                                </li>
                            @endif
                            
                            @if($totalNotifications > 0)
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item text-center" href="{{ route('admin.messages') }}">
                                        View All Notifications
                                    </a>
                                </li>
                            @endif
                        </ul>
                    </div>

                    <!-- Quick Actions -->
                    <div class="dropdown me-3">
                        <button class="btn btn-primary dropdown-toggle" 
                                type="button" 
                                id="quickActionsDropdown" 
                                data-bs-toggle="dropdown" 
                                aria-expanded="false">
                            <i class="fas fa-plus me-1"></i>
                            <span class="d-none d-md-inline">Quick Add</span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="quickActionsDropdown">
                            <li>
                                <a class="dropdown-item" href="{{ route('admin.projects') }}#addProject">
                                    <i class="fas fa-building me-2"></i>Add Project
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ route('admin.services') }}#addService">
                                    <i class="fas fa-tools me-2"></i>Add Service
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ route('admin.media') }}#addMedia">
                                    <i class="fas fa-images me-2"></i>Upload Media
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{{ route('admin.content') }}">
                                    <i class="fas fa-file-alt me-2"></i>Manage Content
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- User Menu -->
                    <div class="dropdown">
                        <button class="btn btn-link text-dark d-flex align-items-center" 
                                type="button" 
                                id="userMenuDropdown" 
                                data-bs-toggle="dropdown" 
                                aria-expanded="false">
                            @if(auth()->user()->avatar)
                                <img src="{{ asset('storage/' . auth()->user()->avatar) }}" 
                                     alt="{{ auth()->user()->name }}" 
                                     class="user-avatar me-2">
                            @else
                                <div class="user-avatar-placeholder me-2">
                                    <i class="fas fa-user"></i>
                                </div>
                            @endif
                            <span class="d-none d-md-inline">{{ auth()->user()->name }}</span>
                            <i class="fas fa-chevron-down ms-2"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userMenuDropdown">
                            <li class="dropdown-header">
                                <div class="text-center">
                                    <div class="fw-bold">{{ auth()->user()->name }}</div>
                                    <small class="text-muted">{{ auth()->user()->email }}</small>
                                </div>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{{ route('admin.profile') }}">
                                    <i class="fas fa-user-circle me-2"></i>Profile
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ route('admin.settings') }}">
                                    <i class="fas fa-cog me-2"></i>Settings
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{{ route('home') }}" target="_blank">
                                    <i class="fas fa-external-link-alt me-2"></i>View Website
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form action="{{ route('admin.logout') }}" method="POST" class="d-inline">
                                    @csrf
                                    <button type="submit" class="dropdown-item">
                                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>
