@extends('layouts.app')

@section('title', 'Media Gallery - Our Construction Work')
@section('description', 'Browse our comprehensive media gallery showcasing our construction projects, before and after photos, and construction process videos.')

@section('content')
<!-- Media Hero Section -->
<section class="media-hero-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-4 fw-bold mb-4">Media Gallery</h1>
                <p class="lead text-muted mb-4">
                    Explore our collection of project photos and videos showcasing our construction expertise and completed works.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Filter Section -->
<section class="filter-section bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="d-flex flex-wrap justify-content-center gap-3">
                    <a href="{{ route('media') }}" 
                       class="btn {{ !request('type') ? 'btn-primary' : 'btn-outline-primary' }}">
                        All Media
                    </a>
                    <a href="{{ route('media', ['type' => 'image']) }}" 
                       class="btn {{ request('type') === 'image' ? 'btn-primary' : 'btn-outline-primary' }}">
                        <i class="fas fa-image me-2"></i>Photos
                    </a>
                    <a href="{{ route('media', ['type' => 'video']) }}" 
                       class="btn {{ request('type') === 'video' ? 'btn-primary' : 'btn-outline-primary' }}">
                        <i class="fas fa-video me-2"></i>Videos
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Media Grid -->
<section class="section-padding">
    <div class="container">
        @if($media->count() > 0)
            <div class="row g-4" id="mediaGrid">
                @foreach($media as $item)
                    <div class="col-lg-3 col-md-4 col-sm-6 media-item" data-type="{{ $item->file_type }}">
                        <div class="media-card">
                            @if($item->file_type === 'image')
                                <div class="media-thumbnail" onclick="openLightbox('{{ asset('storage/' . $item->file_path) }}', '{{ $item->title }}', 'image')">
                                    <img src="{{ asset('storage/' . $item->file_path) }}" 
                                         alt="{{ $item->title }}" 
                                         class="img-fluid rounded">
                                    <div class="media-overlay">
                                        <i class="fas fa-search-plus fa-2x"></i>
                                    </div>
                                </div>
                            @elseif($item->file_type === 'video')
                                <div class="media-thumbnail video-thumbnail" onclick="openLightbox('{{ asset('storage/' . $item->file_path) }}', '{{ $item->title }}', 'video')">
                                    @if($item->thumbnail)
                                        <img src="{{ asset('storage/' . $item->thumbnail) }}" 
                                             alt="{{ $item->title }}" 
                                             class="img-fluid rounded">
                                    @else
                                        <div class="video-placeholder">
                                            <i class="fas fa-video fa-3x text-white"></i>
                                        </div>
                                    @endif
                                    <div class="media-overlay">
                                        <i class="fas fa-play fa-2x"></i>
                                    </div>
                                </div>
                            @endif
                            
                            <div class="media-info mt-3">
                                <h5 class="media-title">{{ $item->title }}</h5>
                                @if($item->description)
                                    <p class="media-description text-muted small">{{ Str::limit($item->description, 80) }}</p>
                                @endif
                                @if($item->category)
                                    <span class="badge bg-primary">{{ $item->category }}</span>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
            
            <!-- Pagination -->
            <div class="row mt-5">
                <div class="col-12">
                    {{ $media->links() }}
                </div>
            </div>
        @else
            <div class="row">
                <div class="col-12 text-center">
                    <div class="empty-state">
                        <i class="fas fa-images fa-4x text-muted mb-3"></i>
                        <h3 class="text-muted">No Media Found</h3>
                        <p class="text-muted">There are no media files available at the moment.</p>
                        <a href="{{ route('contact') }}" class="btn btn-primary">Contact Us</a>
                    </div>
                </div>
            </div>
        @endif
    </div>
</section>

<!-- Lightbox Modal -->
<div class="modal fade" id="mediaLightbox" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content bg-dark">
            <div class="modal-header border-0">
                <h5 class="modal-title text-white" id="lightboxTitle"></h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center p-0">
                <div id="lightboxContent"></div>
            </div>
        </div>
    </div>
</div>

<!-- Call to Action -->
<section class="section-padding bg-primary text-white">
    <div class="container text-center">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <h2 class="display-5 fw-bold mb-3">Like What You See?</h2>
                <p class="lead mb-4">
                    Ready to start your own construction project? Contact us today for a free consultation and quote.
                </p>
                <div class="d-flex flex-wrap justify-content-center gap-3">
                    <a href="{{ route('contact') }}" class="btn btn-light btn-lg">Get Free Quote</a>
                    <a href="{{ route('projects') }}" class="btn btn-outline-light btn-lg">View Projects</a>
                </div>
            </div>
        </div>
    </div>
</section>

@push('scripts')
<script>
function openLightbox(src, title, type) {
    const lightbox = new bootstrap.Modal(document.getElementById('mediaLightbox'));
    const lightboxTitle = document.getElementById('lightboxTitle');
    const lightboxContent = document.getElementById('lightboxContent');
    
    lightboxTitle.textContent = title;
    
    if (type === 'image') {
        lightboxContent.innerHTML = `<img src="${src}" alt="${title}" class="img-fluid">`;
    } else if (type === 'video') {
        lightboxContent.innerHTML = `
            <video controls class="w-100" style="max-height: 80vh;">
                <source src="${src}" type="video/mp4">
                Your browser does not support the video tag.
            </video>
        `;
    }
    
    lightbox.show();
}

// Close lightbox with escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const lightbox = bootstrap.Modal.getInstance(document.getElementById('mediaLightbox'));
        if (lightbox) {
            lightbox.hide();
        }
    }
});
</script>
@endpush

@push('styles')
<style>
.media-hero-section {
    padding: 4rem 0 2rem;
}

.filter-section {
    padding: 2rem 0;
}

.media-card {
    position: relative;
    transition: transform 0.3s ease;
}

.media-card:hover {
    transform: translateY(-5px);
}

.media-thumbnail {
    position: relative;
    overflow: hidden;
    border-radius: 0.5rem;
    cursor: pointer;
    aspect-ratio: 1;
}

.media-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.media-thumbnail:hover img {
    transform: scale(1.1);
}

.video-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.5rem;
}

.media-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 0.5rem;
    color: white;
}

.media-thumbnail:hover .media-overlay {
    opacity: 1;
}

.media-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.media-description {
    margin-bottom: 0.5rem;
}

.empty-state {
    padding: 4rem 2rem;
}

.section-padding {
    padding: 4rem 0;
}

@media (max-width: 768px) {
    .section-padding {
        padding: 2rem 0;
    }
    
    .media-hero-section {
        padding: 2rem 0 1rem;
    }
    
    .filter-section {
        padding: 1rem 0;
    }
}

/* Lightbox styles */
.modal-xl {
    max-width: 90vw;
}

#lightboxContent img,
#lightboxContent video {
    max-height: 80vh;
    width: auto;
}
</style>
@endpush
@endsection
