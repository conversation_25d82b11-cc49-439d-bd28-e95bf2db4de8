@extends('layouts.app')

@section('title', $service->title . ' - Service Details')
@section('description', $service->short_description ?: Str::limit($service->description, 160))

@section('content')
<!-- Service Hero Section -->
<section class="service-hero-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <nav aria-label="breadcrumb" class="mb-4">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('services') }}">Services</a></li>
                        <li class="breadcrumb-item active" aria-current="page">{{ $service->title }}</li>
                    </ol>
                </nav>
                
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <div class="d-flex align-items-center mb-3">
                            @if($service->icon)
                                <i class="{{ $service->icon }} fa-3x text-primary me-3"></i>
                            @endif
                            <div>
                                <h1 class="display-5 fw-bold mb-0">{{ $service->title }}</h1>
                                @if($service->is_featured)
                                    <span class="badge bg-warning text-dark mt-2">
                                        <i class="fas fa-star me-1"></i>Featured Service
                                    </span>
                                @endif
                            </div>
                        </div>
                        
                        @if($service->short_description)
                            <p class="lead text-muted mb-4">{{ $service->short_description }}</p>
                        @endif
                    </div>
                    
                    <div class="col-lg-4 text-end">
                        @if($service->price_from)
                            <div class="price-display">
                                <span class="text-muted">Starting from</span>
                                <div class="h2 text-primary fw-bold mb-0">${{ number_format($service->price_from, 2) }}</div>
                                @if($service->price_unit)
                                    <small class="text-muted">{{ $service->price_unit }}</small>
                                @endif
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Service Image -->
@if($service->image)
<section class="service-image-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <img src="{{ asset('storage/' . $service->image) }}" 
                     alt="{{ $service->title }}" 
                     class="img-fluid rounded shadow-lg w-100"
                     style="height: 400px; object-fit: cover;">
            </div>
        </div>
    </div>
</section>
@endif

<!-- Service Details -->
<section class="section-padding">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <h2 class="h3 fw-bold mb-4">Service Overview</h2>
                <div class="service-description">
                    {!! nl2br(e($service->description)) !!}
                </div>
                
                @if($service->features && count($service->features) > 0)
                    <h3 class="h4 fw-bold mt-5 mb-3">What's Included</h3>
                    <div class="row g-3">
                        @foreach($service->features as $feature)
                            <div class="col-md-6">
                                <div class="d-flex align-items-start">
                                    <i class="fas fa-check-circle text-primary me-2 mt-1"></i>
                                    <span>{{ $feature }}</span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @endif
                
                @if($service->process_steps && count($service->process_steps) > 0)
                    <h3 class="h4 fw-bold mt-5 mb-4">Our Process</h3>
                    <div class="process-steps">
                        @foreach($service->process_steps as $index => $step)
                            <div class="process-step d-flex mb-4">
                                <div class="step-number">
                                    <span class="badge bg-primary rounded-circle p-3 fs-5">{{ $index + 1 }}</span>
                                </div>
                                <div class="step-content ms-3">
                                    <h5 class="fw-bold">{{ $step['title'] ?? 'Step ' . ($index + 1) }}</h5>
                                    <p class="text-muted mb-0">{{ $step['description'] ?? $step }}</p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @endif
            </div>
            
            <div class="col-lg-4">
                <!-- Service Info Card -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="h5 mb-0">Service Information</h3>
                    </div>
                    <div class="card-body">
                        @if($service->price_from)
                            <div class="mb-3">
                                <strong>Starting Price:</strong><br>
                                ${{ number_format($service->price_from, 2) }}
                                @if($service->price_unit)
                                    <small class="text-muted">{{ $service->price_unit }}</small>
                                @endif
                            </div>
                        @endif
                        
                        @if($service->duration)
                            <div class="mb-3">
                                <strong>Typical Duration:</strong><br>
                                {{ $service->duration }}
                            </div>
                        @endif
                        
                        @if($service->warranty_period)
                            <div class="mb-3">
                                <strong>Warranty:</strong><br>
                                {{ $service->warranty_period }}
                            </div>
                        @endif
                        
                        <div class="mb-3">
                            <strong>Availability:</strong><br>
                            <span class="badge bg-success">{{ $service->is_active ? 'Available' : 'Not Available' }}</span>
                        </div>
                    </div>
                </div>
                
                <!-- Contact CTA -->
                <div class="card mt-4">
                    <div class="card-body text-center">
                        <h4 class="h5 mb-3">Ready to Get Started?</h4>
                        <p class="text-muted mb-3">Contact us for a personalized quote and consultation</p>
                        <a href="{{ route('contact') }}" class="btn btn-primary btn-lg w-100 mb-2">Get Free Quote</a>
                        <a href="tel:+1234567890" class="btn btn-outline-primary w-100">
                            <i class="fas fa-phone me-2"></i>Call Now
                        </a>
                    </div>
                </div>
                
                <!-- FAQ Section -->
                @if($service->faqs && count($service->faqs) > 0)
                <div class="card mt-4">
                    <div class="card-header">
                        <h4 class="h5 mb-0">Frequently Asked Questions</h4>
                    </div>
                    <div class="card-body">
                        <div class="accordion" id="serviceFAQ">
                            @foreach($service->faqs as $index => $faq)
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="faq{{ $index }}">
                                        <button class="accordion-button collapsed" type="button" 
                                                data-bs-toggle="collapse" data-bs-target="#collapse{{ $index }}" 
                                                aria-expanded="false" aria-controls="collapse{{ $index }}">
                                            {{ $faq['question'] }}
                                        </button>
                                    </h2>
                                    <div id="collapse{{ $index }}" class="accordion-collapse collapse" 
                                         aria-labelledby="faq{{ $index }}" data-bs-parent="#serviceFAQ">
                                        <div class="accordion-body">
                                            {{ $faq['answer'] }}
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</section>

<!-- Related Services -->
<section class="section-padding bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h2 class="h3 fw-bold mb-4">Other Services</h2>
                
                <div class="row g-4">
                    @php
                        $relatedServices = \App\Models\Service::active()
                            ->where('id', '!=', $service->id)
                            ->take(3)
                            ->get();
                    @endphp
                    
                    @foreach($relatedServices as $relatedService)
                    <div class="col-lg-4 col-md-6">
                        <div class="card h-100 text-center">
                            @if($relatedService->image)
                                <img src="{{ asset('storage/' . $relatedService->image) }}" 
                                     class="card-img-top" 
                                     alt="{{ $relatedService->title }}" 
                                     style="height: 200px; object-fit: cover;">
                            @endif
                            <div class="card-body">
                                @if($relatedService->icon)
                                    <i class="{{ $relatedService->icon }} fa-2x text-primary mb-3"></i>
                                @endif
                                <h5 class="card-title">{{ $relatedService->title }}</h5>
                                <p class="card-text">{{ $relatedService->short_description ?: Str::limit($relatedService->description, 100) }}</p>
                                @if($relatedService->price_from)
                                    <p class="text-primary fw-bold">Starting from ${{ number_format($relatedService->price_from, 2) }}</p>
                                @endif
                            </div>
                            <div class="card-footer bg-transparent">
                                <a href="{{ route('service', $relatedService->slug) }}" class="btn btn-outline-primary">Learn More</a>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
                
                <div class="text-center mt-4">
                    <a href="{{ route('services') }}" class="btn btn-primary">View All Services</a>
                </div>
            </div>
        </div>
    </div>
</section>

@push('styles')
<style>
.service-hero-section {
    padding: 2rem 0;
}

.service-image-section {
    padding: 2rem 0;
}

.service-description {
    font-size: 1.1rem;
    line-height: 1.8;
}

.process-step {
    position: relative;
}

.process-step:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 1.5rem;
    top: 4rem;
    width: 2px;
    height: 2rem;
    background-color: #dee2e6;
}

.step-number .badge {
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.price-display {
    text-align: center;
    padding: 1rem;
    background: rgba(var(--bs-primary-rgb), 0.1);
    border-radius: 0.5rem;
}

.section-padding {
    padding: 4rem 0;
}

@media (max-width: 768px) {
    .section-padding {
        padding: 2rem 0;
    }
    
    .service-hero-section,
    .service-image-section {
        padding: 1rem 0;
    }
}
</style>
@endpush
@endsection
