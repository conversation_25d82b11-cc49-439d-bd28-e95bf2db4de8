<!-- Admin Header -->
<header class="admin-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <!-- Mobile Menu Toggle -->
            <div class="col-auto d-lg-none">
                <button class="btn btn-link text-dark" id="mobileMenuToggle">
                    <i class="fas fa-bars fa-lg"></i>
                </button>
            </div>

            <!-- Page Title -->
            <div class="col">
                <div class="page-title-section">
                    <h1 class="page-title mb-0"><?php echo $__env->yieldContent('page-title', 'Dashboard'); ?></h1>
                    <?php if (! empty(trim($__env->yieldContent('page-subtitle')))): ?>
                        <p class="page-subtitle text-muted mb-0"><?php echo $__env->yieldContent('page-subtitle'); ?></p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Header Actions -->
            <div class="col-auto">
                <div class="header-actions d-flex align-items-center">
                    <!-- Notifications -->
                    <div class="dropdown me-3">
                        <button class="btn btn-link text-dark position-relative" 
                                type="button" 
                                id="notificationsDropdown" 
                                data-bs-toggle="dropdown" 
                                aria-expanded="false">
                            <i class="fas fa-bell fa-lg"></i>
                            <?php
                                $unreadMessages = \App\Models\Message::where('is_read', false)->count();
                                $totalNotifications = $unreadMessages;
                            ?>
                            <?php if($totalNotifications > 0): ?>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                    <?php echo e($totalNotifications > 99 ? '99+' : $totalNotifications); ?>

                                </span>
                            <?php endif; ?>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end notifications-dropdown" aria-labelledby="notificationsDropdown">
                            <li class="dropdown-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>Notifications</span>
                                    <?php if($totalNotifications > 0): ?>
                                        <span class="badge bg-primary"><?php echo e($totalNotifications); ?></span>
                                    <?php endif; ?>
                                </div>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            
                            <?php if($unreadMessages > 0): ?>
                                <li>
                                    <a class="dropdown-item" href="<?php echo e(route('admin.messages')); ?>">
                                        <div class="d-flex align-items-center">
                                            <div class="notification-icon bg-primary">
                                                <i class="fas fa-envelope"></i>
                                            </div>
                                            <div class="notification-content">
                                                <div class="notification-title">New Messages</div>
                                                <div class="notification-text"><?php echo e($unreadMessages); ?> unread message<?php echo e($unreadMessages > 1 ? 's' : ''); ?></div>
                                            </div>
                                        </div>
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <?php if($totalNotifications === 0): ?>
                                <li>
                                    <div class="dropdown-item-text text-center text-muted py-3">
                                        <i class="fas fa-check-circle fa-2x mb-2"></i><br>
                                        No new notifications
                                    </div>
                                </li>
                            <?php endif; ?>
                            
                            <?php if($totalNotifications > 0): ?>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item text-center" href="<?php echo e(route('admin.messages')); ?>">
                                        View All Notifications
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </div>

                    <!-- Quick Actions -->
                    <div class="dropdown me-3">
                        <button class="btn btn-primary dropdown-toggle" 
                                type="button" 
                                id="quickActionsDropdown" 
                                data-bs-toggle="dropdown" 
                                aria-expanded="false">
                            <i class="fas fa-plus me-1"></i>
                            <span class="d-none d-md-inline">Quick Add</span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="quickActionsDropdown">
                            <li>
                                <a class="dropdown-item" href="<?php echo e(route('admin.projects')); ?>#addProject">
                                    <i class="fas fa-building me-2"></i>Add Project
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="<?php echo e(route('admin.services')); ?>#addService">
                                    <i class="fas fa-tools me-2"></i>Add Service
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="<?php echo e(route('admin.media')); ?>#addMedia">
                                    <i class="fas fa-images me-2"></i>Upload Media
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="<?php echo e(route('admin.content')); ?>">
                                    <i class="fas fa-file-alt me-2"></i>Manage Content
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- User Menu -->
                    <div class="dropdown">
                        <button class="btn btn-link text-dark d-flex align-items-center" 
                                type="button" 
                                id="userMenuDropdown" 
                                data-bs-toggle="dropdown" 
                                aria-expanded="false">
                            <?php if(auth()->user()->avatar): ?>
                                <img src="<?php echo e(asset('storage/' . auth()->user()->avatar)); ?>" 
                                     alt="<?php echo e(auth()->user()->name); ?>" 
                                     class="user-avatar me-2">
                            <?php else: ?>
                                <div class="user-avatar-placeholder me-2">
                                    <i class="fas fa-user"></i>
                                </div>
                            <?php endif; ?>
                            <span class="d-none d-md-inline"><?php echo e(auth()->user()->name); ?></span>
                            <i class="fas fa-chevron-down ms-2"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userMenuDropdown">
                            <li class="dropdown-header">
                                <div class="text-center">
                                    <div class="fw-bold"><?php echo e(auth()->user()->name); ?></div>
                                    <small class="text-muted"><?php echo e(auth()->user()->email); ?></small>
                                </div>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="<?php echo e(route('admin.profile')); ?>">
                                    <i class="fas fa-user-circle me-2"></i>Profile
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="<?php echo e(route('admin.settings')); ?>">
                                    <i class="fas fa-cog me-2"></i>Settings
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="<?php echo e(route('home')); ?>" target="_blank">
                                    <i class="fas fa-external-link-alt me-2"></i>View Website
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form action="<?php echo e(route('admin.logout')); ?>" method="POST" class="d-inline">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="dropdown-item">
                                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>
<?php /**PATH C:\xampp\htdocs\new-project-app\resources\views/admin/partials/header.blade.php ENDPATH**/ ?>