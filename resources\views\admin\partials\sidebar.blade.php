<!-- Admin Sidebar -->
<div class="admin-sidebar" id="adminSidebar">
    <div class="sidebar-header">
        <div class="d-flex align-items-center">
            <div class="sidebar-logo">
                <i class="fas fa-hard-hat text-primary"></i>
            </div>
            <div class="sidebar-brand ms-2">
                <h5 class="mb-0 text-white">ConstructCo</h5>
                <small class="text-muted">Admin Panel</small>
            </div>
        </div>
        <button class="btn btn-link text-white d-lg-none" id="sidebarToggle">
            <i class="fas fa-times"></i>
        </button>
    </div>

    <div class="sidebar-content">
        <!-- User Profile Section -->
        <div class="user-profile">
            <div class="d-flex align-items-center">
                @if(auth()->user()->avatar)
                    <img src="{{ asset('storage/' . auth()->user()->avatar) }}" 
                         alt="{{ auth()->user()->name }}" 
                         class="user-avatar">
                @else
                    <div class="user-avatar-placeholder">
                        <i class="fas fa-user"></i>
                    </div>
                @endif
                <div class="user-info ms-2">
                    <div class="user-name">{{ auth()->user()->name }}</div>
                    <div class="user-role">{{ ucfirst(auth()->user()->role ?? 'Admin') }}</div>
                </div>
            </div>
        </div>

        <!-- Navigation Menu -->
        <nav class="sidebar-nav">
            <ul class="nav flex-column">
                <!-- Dashboard -->
                <li class="nav-item">
                    <a href="{{ route('admin.dashboard') }}" 
                       class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
                        <i class="fas fa-tachometer-alt nav-icon"></i>
                        <span class="nav-text">Dashboard</span>
                    </a>
                </li>

                <!-- Projects -->
                <li class="nav-item">
                    <a href="{{ route('admin.projects') }}" 
                       class="nav-link {{ request()->routeIs('admin.projects*') ? 'active' : '' }}">
                        <i class="fas fa-building nav-icon"></i>
                        <span class="nav-text">Projects</span>
                        @php
                            $projectsCount = \App\Models\Project::count();
                        @endphp
                        @if($projectsCount > 0)
                            <span class="nav-badge">{{ $projectsCount }}</span>
                        @endif
                    </a>
                </li>

                <!-- Services -->
                <li class="nav-item">
                    <a href="{{ route('admin.services') }}" 
                       class="nav-link {{ request()->routeIs('admin.services*') ? 'active' : '' }}">
                        <i class="fas fa-tools nav-icon"></i>
                        <span class="nav-text">Services</span>
                        @php
                            $servicesCount = \App\Models\Service::count();
                        @endphp
                        @if($servicesCount > 0)
                            <span class="nav-badge">{{ $servicesCount }}</span>
                        @endif
                    </a>
                </li>

                <!-- Media -->
                <li class="nav-item">
                    <a href="{{ route('admin.media') }}" 
                       class="nav-link {{ request()->routeIs('admin.media*') ? 'active' : '' }}">
                        <i class="fas fa-images nav-icon"></i>
                        <span class="nav-text">Media</span>
                        @php
                            $mediaCount = \App\Models\Media::count();
                        @endphp
                        @if($mediaCount > 0)
                            <span class="nav-badge">{{ $mediaCount }}</span>
                        @endif
                    </a>
                </li>

                <!-- Messages -->
                <li class="nav-item">
                    <a href="{{ route('admin.messages') }}" 
                       class="nav-link {{ request()->routeIs('admin.messages*') ? 'active' : '' }}">
                        <i class="fas fa-envelope nav-icon"></i>
                        <span class="nav-text">Messages</span>
                        @php
                            $unreadCount = \App\Models\Message::where('is_read', false)->count();
                        @endphp
                        @if($unreadCount > 0)
                            <span class="nav-badge bg-danger">{{ $unreadCount }}</span>
                        @endif
                    </a>
                </li>

                <!-- Content -->
                <li class="nav-item">
                    <a href="{{ route('admin.content') }}" 
                       class="nav-link {{ request()->routeIs('admin.content*') ? 'active' : '' }}">
                        <i class="fas fa-file-alt nav-icon"></i>
                        <span class="nav-text">Content</span>
                    </a>
                </li>

                <!-- Divider -->
                <li class="nav-divider"></li>

                <!-- Settings -->
                <li class="nav-item">
                    <a href="{{ route('admin.settings') }}" 
                       class="nav-link {{ request()->routeIs('admin.settings*') ? 'active' : '' }}">
                        <i class="fas fa-cog nav-icon"></i>
                        <span class="nav-text">Settings</span>
                    </a>
                </li>

                <!-- Profile -->
                <li class="nav-item">
                    <a href="{{ route('admin.profile') }}" 
                       class="nav-link {{ request()->routeIs('admin.profile*') ? 'active' : '' }}">
                        <i class="fas fa-user-circle nav-icon"></i>
                        <span class="nav-text">Profile</span>
                    </a>
                </li>

                <!-- Divider -->
                <li class="nav-divider"></li>

                <!-- Frontend Links -->
                <li class="nav-item">
                    <a href="{{ route('home') }}" 
                       class="nav-link" 
                       target="_blank">
                        <i class="fas fa-external-link-alt nav-icon"></i>
                        <span class="nav-text">View Website</span>
                    </a>
                </li>

                <!-- Logout -->
                <li class="nav-item">
                    <form action="{{ route('admin.logout') }}" method="POST" class="d-inline">
                        @csrf
                        <button type="submit" class="nav-link btn btn-link text-start w-100 border-0 p-0">
                            <i class="fas fa-sign-out-alt nav-icon"></i>
                            <span class="nav-text">Logout</span>
                        </button>
                    </form>
                </li>
            </ul>
        </nav>
    </div>

    <!-- Sidebar Footer -->
    <div class="sidebar-footer">
        <div class="text-center">
            <small class="text-muted">
                © {{ date('Y') }} ConstructCo<br>
                Admin Panel v1.0
            </small>
        </div>
    </div>
</div>

<!-- Sidebar Overlay for Mobile -->
<div class="sidebar-overlay d-lg-none" id="sidebarOverlay"></div>

<style>
.admin-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: 280px;
    background: linear-gradient(180deg, #023047 0%, #034663 100%);
    color: white;
    z-index: 1050;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.admin-sidebar.show {
    transform: translateX(0);
}

@media (min-width: 992px) {
    .admin-sidebar {
        position: relative;
        transform: translateX(0);
    }
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-logo {
    font-size: 1.5rem;
}

.sidebar-content {
    flex: 1;
    padding: 1rem 0;
}

.user-profile {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 1rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.user-avatar-placeholder {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-name {
    font-weight: 600;
    font-size: 0.9rem;
}

.user-role {
    font-size: 0.8rem;
    color: #FFB703;
}

.sidebar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1.5rem;
    border-radius: 0;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    text-decoration: none;
    border: none;
    background: none;
}

.sidebar-nav .nav-link:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
}

.sidebar-nav .nav-link.active {
    color: white;
    background: rgba(255, 183, 3, 0.2);
    border-right: 3px solid #FFB703;
}

.nav-icon {
    width: 20px;
    margin-right: 0.75rem;
    text-align: center;
}

.nav-text {
    flex: 1;
}

.nav-badge {
    background: #FFB703;
    color: #023047;
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    font-weight: 600;
}

.nav-badge.bg-danger {
    background: #dc3545 !important;
    color: white !important;
}

.nav-divider {
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
    margin: 1rem 1.5rem;
}

.sidebar-footer {
    padding: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1040;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
}
</style>
