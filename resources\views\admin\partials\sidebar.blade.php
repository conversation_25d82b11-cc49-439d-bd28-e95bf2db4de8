<!-- Admin Sidebar -->
<nav class="admin-sidebar" id="adminSidebar">
    <div class="sidebar-header">
        <div class="d-flex align-items-center flex-grow-1">
            <div class="sidebar-logo">
                <i class="fas fa-hard-hat text-warning"></i>
            </div>
            <div class="sidebar-brand ms-2">
                <h5 class="mb-0 text-white">ConstructCo</h5>
                <small class="text-warning">Admin Panel</small>
            </div>
        </div>
        <button class="btn btn-link text-white d-lg-none p-1" id="sidebarToggle">
            <i class="fas fa-times"></i>
        </button>
    </div>

    <div class="sidebar-content">
        <!-- User Profile Section -->
        <div class="user-profile">
            <div class="d-flex align-items-center">
                @if(auth()->user()->avatar)
                    <img src="{{ asset('storage/' . auth()->user()->avatar) }}" 
                         alt="{{ auth()->user()->name }}" 
                         class="user-avatar">
                @else
                    <div class="user-avatar-placeholder">
                        <i class="fas fa-user"></i>
                    </div>
                @endif
                <div class="user-info ms-2">
                    <div class="user-name">{{ auth()->user()->name }}</div>
                    <div class="user-role">{{ ucfirst(auth()->user()->role ?? 'Admin') }}</div>
                </div>
            </div>
        </div>

        <!-- Navigation Menu -->
        <nav class="sidebar-nav">
            <ul class="nav flex-column">
                <!-- Dashboard -->
                <li class="nav-item">
                    <a href="{{ route('admin.dashboard') }}" 
                       class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
                        <i class="fas fa-tachometer-alt nav-icon"></i>
                        <span class="nav-text">Dashboard</span>
                    </a>
                </li>

                <!-- Projects -->
                <li class="nav-item">
                    <a href="{{ route('admin.projects') }}" 
                       class="nav-link {{ request()->routeIs('admin.projects*') ? 'active' : '' }}">
                        <i class="fas fa-building nav-icon"></i>
                        <span class="nav-text">Projects</span>
                        @php
                            $projectsCount = \App\Models\Project::count();
                        @endphp
                        @if($projectsCount > 0)
                            <span class="nav-badge">{{ $projectsCount }}</span>
                        @endif
                    </a>
                </li>

                <!-- Services -->
                <li class="nav-item">
                    <a href="{{ route('admin.services') }}" 
                       class="nav-link {{ request()->routeIs('admin.services*') ? 'active' : '' }}">
                        <i class="fas fa-tools nav-icon"></i>
                        <span class="nav-text">Services</span>
                        @php
                            $servicesCount = \App\Models\Service::count();
                        @endphp
                        @if($servicesCount > 0)
                            <span class="nav-badge">{{ $servicesCount }}</span>
                        @endif
                    </a>
                </li>

                <!-- Media -->
                <li class="nav-item">
                    <a href="{{ route('admin.media') }}" 
                       class="nav-link {{ request()->routeIs('admin.media*') ? 'active' : '' }}">
                        <i class="fas fa-images nav-icon"></i>
                        <span class="nav-text">Media</span>
                        @php
                            $mediaCount = \App\Models\Media::count();
                        @endphp
                        @if($mediaCount > 0)
                            <span class="nav-badge">{{ $mediaCount }}</span>
                        @endif
                    </a>
                </li>

                <!-- Messages -->
                <li class="nav-item">
                    <a href="{{ route('admin.messages') }}" 
                       class="nav-link {{ request()->routeIs('admin.messages*') ? 'active' : '' }}">
                        <i class="fas fa-envelope nav-icon"></i>
                        <span class="nav-text">Messages</span>
                        @php
                            $unreadCount = \App\Models\Message::where('is_read', false)->count();
                        @endphp
                        @if($unreadCount > 0)
                            <span class="nav-badge bg-danger">{{ $unreadCount }}</span>
                        @endif
                    </a>
                </li>

                <!-- Content -->
                <li class="nav-item">
                    <a href="{{ route('admin.content') }}" 
                       class="nav-link {{ request()->routeIs('admin.content*') ? 'active' : '' }}">
                        <i class="fas fa-file-alt nav-icon"></i>
                        <span class="nav-text">Content</span>
                    </a>
                </li>

                <!-- Divider -->
                <li class="nav-divider"></li>

                <!-- Settings -->
                <li class="nav-item">
                    <a href="{{ route('admin.settings') }}" 
                       class="nav-link {{ request()->routeIs('admin.settings*') ? 'active' : '' }}">
                        <i class="fas fa-cog nav-icon"></i>
                        <span class="nav-text">Settings</span>
                    </a>
                </li>

                <!-- Profile -->
                <li class="nav-item">
                    <a href="{{ route('admin.profile') }}" 
                       class="nav-link {{ request()->routeIs('admin.profile*') ? 'active' : '' }}">
                        <i class="fas fa-user-circle nav-icon"></i>
                        <span class="nav-text">Profile</span>
                    </a>
                </li>

                <!-- Divider -->
                <li class="nav-divider"></li>

                <!-- Frontend Links -->
                <li class="nav-item">
                    <a href="{{ route('home') }}" 
                       class="nav-link" 
                       target="_blank">
                        <i class="fas fa-external-link-alt nav-icon"></i>
                        <span class="nav-text">View Website</span>
                    </a>
                </li>

                <!-- Logout -->
                <li class="nav-item">
                    <form action="{{ route('admin.logout') }}" method="POST" class="d-inline w-100">
                        @csrf
                        <button type="submit" class="nav-link btn btn-link text-start w-100 border-0 text-white-50">
                            <i class="fas fa-sign-out-alt nav-icon"></i>
                            <span class="nav-text">Logout</span>
                        </button>
                    </form>
                </li>
            </ul>
        </nav>
    </div>

    <!-- Sidebar Footer -->
    <div class="sidebar-footer">
        <div class="text-center">
            <small class="text-white-50">
                © {{ date('Y') }} ConstructCo<br>
                Admin Panel v1.0
            </small>
        </div>
    </div>
</nav>

<!-- Sidebar Overlay for Mobile -->
<div class="sidebar-overlay d-lg-none" id="sidebarOverlay"></div>
