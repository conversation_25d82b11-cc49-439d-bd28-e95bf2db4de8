<!-- Admin Footer -->
<footer class="admin-footer">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-6">
                <div class="footer-info">
                    <span class="text-muted">
                        © {{ date('Y') }} <strong>ConstructCo</strong> Admin Panel. 
                        All rights reserved.
                    </span>
                </div>
            </div>
            <div class="col-md-6">
                <div class="footer-links text-md-end">
                    <div class="d-flex flex-wrap justify-content-md-end gap-3">
                        <!-- System Status -->
                        <div class="system-status">
                            <span class="status-indicator status-online"></span>
                            <small class="text-muted">System Online</small>
                        </div>
                        
                        <!-- Version Info -->
                        <div class="version-info">
                            <small class="text-muted">
                                <i class="fas fa-code-branch me-1"></i>
                                v1.0.0
                            </small>
                        </div>
                        
                        <!-- Last Updated -->
                        <div class="last-updated">
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                Updated: {{ date('M d, Y') }}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Additional Footer Content -->
        <div class="row mt-3">
            <div class="col-12">
                <div class="footer-stats">
                    <div class="row text-center">
                        <div class="col-6 col-md-3">
                            <div class="stat-item">
                                <div class="stat-number">{{ \App\Models\Project::count() }}</div>
                                <div class="stat-label">Projects</div>
                            </div>
                        </div>
                        <div class="col-6 col-md-3">
                            <div class="stat-item">
                                <div class="stat-number">{{ \App\Models\Service::count() }}</div>
                                <div class="stat-label">Services</div>
                            </div>
                        </div>
                        <div class="col-6 col-md-3">
                            <div class="stat-item">
                                <div class="stat-number">{{ \App\Models\Media::count() }}</div>
                                <div class="stat-label">Media Files</div>
                            </div>
                        </div>
                        <div class="col-6 col-md-3">
                            <div class="stat-item">
                                <div class="stat-number">{{ \App\Models\Message::where('is_read', false)->count() }}</div>
                                <div class="stat-label">Unread Messages</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>

<style>
.admin-footer {
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    padding: 1.5rem 0;
    margin-top: auto;
    color: #6c757d;
}

.footer-info {
    font-size: 0.9rem;
}

.footer-links {
    font-size: 0.85rem;
}

.system-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
}

.status-online {
    background-color: #28a745;
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.3);
}

.status-offline {
    background-color: #dc3545;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.3);
}

.status-maintenance {
    background-color: #ffc107;
    box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.3);
}

.footer-stats {
    border-top: 1px solid #e9ecef;
    padding-top: 1rem;
    margin-top: 1rem;
}

.stat-item {
    padding: 0.5rem;
}

.stat-number {
    font-size: 1.25rem;
    font-weight: 600;
    color: #023047;
}

.stat-label {
    font-size: 0.8rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

@media (max-width: 768px) {
    .admin-footer {
        padding: 1rem 0;
    }
    
    .footer-links {
        margin-top: 1rem;
    }
    
    .footer-links .d-flex {
        justify-content: center !important;
    }
    
    .stat-number {
        font-size: 1.1rem;
    }
    
    .stat-label {
        font-size: 0.75rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .admin-footer {
        background: #2d3748;
        border-top-color: #4a5568;
        color: #a0aec0;
    }
    
    .stat-number {
        color: #e2e8f0;
    }
    
    .stat-label {
        color: #a0aec0;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update system status periodically
    function updateSystemStatus() {
        // This would typically make an AJAX call to check system health
        // For now, we'll just simulate it
        const statusIndicator = document.querySelector('.status-indicator');
        const statusText = statusIndicator?.nextElementSibling;
        
        if (statusIndicator && statusText) {
            // Simulate system check
            fetch('/admin/system-status', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                statusIndicator.className = `status-indicator status-${data.status}`;
                statusText.textContent = `System ${data.status.charAt(0).toUpperCase() + data.status.slice(1)}`;
            })
            .catch(() => {
                // If the request fails, assume system is offline
                statusIndicator.className = 'status-indicator status-offline';
                statusText.textContent = 'System Offline';
            });
        }
    }
    
    // Update status every 5 minutes
    setInterval(updateSystemStatus, 300000);
    
    // Initial status check
    updateSystemStatus();
    
    // Add smooth animations to stat numbers
    function animateNumbers() {
        const statNumbers = document.querySelectorAll('.stat-number');
        
        statNumbers.forEach(element => {
            const finalNumber = parseInt(element.textContent);
            const duration = 1000; // 1 second
            const steps = 20;
            const increment = finalNumber / steps;
            let current = 0;
            
            const timer = setInterval(() => {
                current += increment;
                if (current >= finalNumber) {
                    element.textContent = finalNumber;
                    clearInterval(timer);
                } else {
                    element.textContent = Math.floor(current);
                }
            }, duration / steps);
        });
    }
    
    // Animate numbers when footer comes into view
    const footer = document.querySelector('.admin-footer');
    if (footer) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateNumbers();
                    observer.unobserve(entry.target);
                }
            });
        });
        
        observer.observe(footer);
    }
});
</script>
