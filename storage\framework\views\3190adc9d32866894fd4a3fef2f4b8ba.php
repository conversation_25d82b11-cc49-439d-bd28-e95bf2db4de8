<!-- Admin Footer -->
<footer class="admin-footer">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-6">
                <div class="footer-info">
                    <span class="text-muted">
                        © <?php echo e(date('Y')); ?> <strong>ConstructCo</strong> Admin Panel. 
                        All rights reserved.
                    </span>
                </div>
            </div>
            <div class="col-md-6">
                <div class="footer-links text-md-end">
                    <div class="d-flex flex-wrap justify-content-md-end gap-3">
                        <!-- System Status -->
                        <div class="system-status">
                            <span class="status-indicator status-online"></span>
                            <small class="text-muted">System Online</small>
                        </div>
                        
                        <!-- Version Info -->
                        <div class="version-info">
                            <small class="text-muted">
                                <i class="fas fa-code-branch me-1"></i>
                                v1.0.0
                            </small>
                        </div>
                        
                        <!-- Last Updated -->
                        <div class="last-updated">
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                Updated: <?php echo e(date('M d, Y')); ?>

                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Additional Footer Content -->
        <div class="row mt-3">
            <div class="col-12">
                <div class="footer-stats">
                    <div class="row text-center">
                        <div class="col-6 col-md-3">
                            <div class="stat-item">
                                <div class="stat-number"><?php echo e(\App\Models\Project::count()); ?></div>
                                <div class="stat-label">Projects</div>
                            </div>
                        </div>
                        <div class="col-6 col-md-3">
                            <div class="stat-item">
                                <div class="stat-number"><?php echo e(\App\Models\Service::count()); ?></div>
                                <div class="stat-label">Services</div>
                            </div>
                        </div>
                        <div class="col-6 col-md-3">
                            <div class="stat-item">
                                <div class="stat-number"><?php echo e(\App\Models\Media::count()); ?></div>
                                <div class="stat-label">Media Files</div>
                            </div>
                        </div>
                        <div class="col-6 col-md-3">
                            <div class="stat-item">
                                <div class="stat-number"><?php echo e(\App\Models\Message::where('is_read', false)->count()); ?></div>
                                <div class="stat-label">Unread Messages</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>
<?php /**PATH C:\xampp\htdocs\new-project-app\resources\views/admin/partials/footer.blade.php ENDPATH**/ ?>