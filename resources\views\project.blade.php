@extends('layouts.app')

@section('title', $project->title . ' - Project Details')
@section('description', $project->short_description ?: Str::limit($project->description, 160))

@section('content')
<!-- Project Hero Section -->
<section class="project-hero-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <nav aria-label="breadcrumb" class="mb-4">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('projects') }}">Projects</a></li>
                        <li class="breadcrumb-item active" aria-current="page">{{ $project->title }}</li>
                    </ol>
                </nav>
                
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <div class="d-flex align-items-center mb-3">
                            <h1 class="display-5 fw-bold mb-0 me-3">{{ $project->title }}</h1>
                            @if($project->category)
                                <span class="badge bg-primary fs-6">{{ $project->category->name }}</span>
                            @endif
                        </div>
                        
                        @if($project->short_description)
                            <p class="lead text-muted mb-4">{{ $project->short_description }}</p>
                        @endif
                        
                        <div class="row g-3 mb-4">
                            @if($project->location)
                                <div class="col-auto">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                        <span>{{ $project->location }}</span>
                                    </div>
                                </div>
                            @endif
                            
                            @if($project->completion_date)
                                <div class="col-auto">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-calendar-check text-primary me-2"></i>
                                        <span>Completed: {{ $project->completion_date->format('M Y') }}</span>
                                    </div>
                                </div>
                            @endif
                            
                            @if($project->project_value)
                                <div class="col-auto">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-dollar-sign text-primary me-2"></i>
                                        <span>Value: ${{ number_format($project->project_value) }}</span>
                                    </div>
                                </div>
                            @endif
                            
                            @if($project->duration_months)
                                <div class="col-auto">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-clock text-primary me-2"></i>
                                        <span>Duration: {{ $project->duration_months }} months</span>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                    
                    <div class="col-lg-4 text-end">
                        @if($project->is_featured)
                            <span class="badge bg-warning text-dark fs-6 mb-2">
                                <i class="fas fa-star me-1"></i>Featured Project
                            </span>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Project Images Gallery -->
@if($project->images && count($project->images) > 0)
<section class="section-padding bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h2 class="h3 fw-bold mb-4">Project Gallery</h2>
                
                <!-- Main Image -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="main-image-container">
                            <img src="{{ asset('storage/' . $project->images[0]) }}" 
                                 alt="{{ $project->title }}" 
                                 class="img-fluid rounded shadow-lg w-100"
                                 style="height: 500px; object-fit: cover;"
                                 id="mainProjectImage">
                        </div>
                    </div>
                </div>
                
                <!-- Thumbnail Images -->
                @if(count($project->images) > 1)
                <div class="row g-3">
                    @foreach($project->images as $index => $image)
                        <div class="col-lg-2 col-md-3 col-4">
                            <img src="{{ asset('storage/' . $image) }}" 
                                 alt="{{ $project->title }} - Image {{ $index + 1 }}" 
                                 class="img-fluid rounded shadow-sm thumbnail-image {{ $index === 0 ? 'active' : '' }}"
                                 style="height: 100px; object-fit: cover; cursor: pointer;"
                                 onclick="changeMainImage('{{ asset('storage/' . $image) }}', this)">
                        </div>
                    @endforeach
                </div>
                @endif
            </div>
        </div>
    </div>
</section>
@endif

<!-- Project Details -->
<section class="section-padding">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <h2 class="h3 fw-bold mb-4">Project Details</h2>
                <div class="project-description">
                    {!! nl2br(e($project->description)) !!}
                </div>
                
                @if($project->features && count($project->features) > 0)
                    <h3 class="h4 fw-bold mt-5 mb-3">Key Features</h3>
                    <ul class="list-unstyled">
                        @foreach($project->features as $feature)
                            <li class="mb-2">
                                <i class="fas fa-check-circle text-primary me-2"></i>
                                {{ $feature }}
                            </li>
                        @endforeach
                    </ul>
                @endif
                
                @if($project->challenges)
                    <h3 class="h4 fw-bold mt-5 mb-3">Challenges & Solutions</h3>
                    <div class="challenges-content">
                        {!! nl2br(e($project->challenges)) !!}
                    </div>
                @endif
            </div>
            
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h3 class="h5 mb-0">Project Information</h3>
                    </div>
                    <div class="card-body">
                        @if($project->client_name)
                            <div class="mb-3">
                                <strong>Client:</strong><br>
                                {{ $project->client_name }}
                            </div>
                        @endif
                        
                        @if($project->category)
                            <div class="mb-3">
                                <strong>Category:</strong><br>
                                {{ $project->category->name }}
                            </div>
                        @endif
                        
                        @if($project->location)
                            <div class="mb-3">
                                <strong>Location:</strong><br>
                                {{ $project->location }}
                            </div>
                        @endif
                        
                        @if($project->start_date)
                            <div class="mb-3">
                                <strong>Start Date:</strong><br>
                                {{ $project->start_date->format('M d, Y') }}
                            </div>
                        @endif
                        
                        @if($project->completion_date)
                            <div class="mb-3">
                                <strong>Completion Date:</strong><br>
                                {{ $project->completion_date->format('M d, Y') }}
                            </div>
                        @endif
                        
                        @if($project->project_value)
                            <div class="mb-3">
                                <strong>Project Value:</strong><br>
                                ${{ number_format($project->project_value) }}
                            </div>
                        @endif
                        
                        @if($project->square_footage)
                            <div class="mb-3">
                                <strong>Square Footage:</strong><br>
                                {{ number_format($project->square_footage) }} sq ft
                            </div>
                        @endif
                    </div>
                </div>
                
                <!-- Contact CTA -->
                <div class="card mt-4">
                    <div class="card-body text-center">
                        <h4 class="h5 mb-3">Interested in Similar Work?</h4>
                        <p class="text-muted mb-3">Get a free consultation for your construction project</p>
                        <a href="{{ route('contact') }}" class="btn btn-primary btn-lg w-100">Get Free Quote</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Projects -->
@if($relatedProjects && count($relatedProjects) > 0)
<section class="section-padding bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h2 class="h3 fw-bold mb-4">Related Projects</h2>

                <div class="row g-4">
                    @foreach($relatedProjects as $relatedProject)
                    <div class="col-lg-4 col-md-6">
                        <div class="card h-100">
                            @if($relatedProject->images && count($relatedProject->images) > 0)
                                <img src="{{ asset('storage/' . $relatedProject->images[0]) }}"
                                     class="card-img-top"
                                     alt="{{ $relatedProject->title }}"
                                     style="height: 200px; object-fit: cover;">
                            @else
                                <img src="https://images.unsplash.com/photo-1590725175499-8b8c8b0c8b0c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                                     class="card-img-top"
                                     alt="{{ $relatedProject->title }}"
                                     style="height: 200px; object-fit: cover;">
                            @endif
                            <div class="card-body">
                                <h5 class="card-title">{{ $relatedProject->title }}</h5>
                                <p class="card-text">{{ $relatedProject->short_description ?: Str::limit($relatedProject->description, 100) }}</p>
                                @if($relatedProject->location)
                                    <p class="text-muted small">
                                        <i class="fas fa-map-marker-alt me-1"></i>{{ $relatedProject->location }}
                                    </p>
                                @endif
                            </div>
                            <div class="card-footer bg-transparent">
                                <a href="{{ route('project', $relatedProject->slug) }}" class="btn btn-outline-primary">View Details</a>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>

                <div class="text-center mt-4">
                    <a href="{{ route('projects') }}" class="btn btn-primary">View All Projects</a>
                </div>
            </div>
        </div>
    </div>
</section>
@endif

@push('scripts')
<script>
function changeMainImage(imageSrc, thumbnail) {
    // Update main image
    document.getElementById('mainProjectImage').src = imageSrc;

    // Update active thumbnail
    document.querySelectorAll('.thumbnail-image').forEach(img => {
        img.classList.remove('active');
    });
    thumbnail.classList.add('active');
}
</script>
@endpush

@push('styles')
<style>
.project-hero-section {
    padding: 2rem 0;
}

.thumbnail-image {
    border: 3px solid transparent;
    transition: all 0.3s ease;
}

.thumbnail-image:hover,
.thumbnail-image.active {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.project-description {
    font-size: 1.1rem;
    line-height: 1.8;
}

.challenges-content {
    font-size: 1rem;
    line-height: 1.7;
}

.section-padding {
    padding: 4rem 0;
}

@media (max-width: 768px) {
    .section-padding {
        padding: 2rem 0;
    }

    .project-hero-section {
        padding: 1rem 0;
    }
}
</style>
@endpush
@endsection
